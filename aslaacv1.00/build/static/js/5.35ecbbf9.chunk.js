(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[5],{1319:function(t,e,n){"use strict";var o=n(12),r=n(3),i=n(0),c=n(31),s=n(541),a=n(52),l=n(47),u=n(67),f=n(690),d=n(229),p=n(611),h=n(542),g=n(516);function b(t){return Object(g.a)("MuiLink",t)}var v=Object(h.a)("MuiLink",["root","underlineNone","underlineHover","underlineAlways","button","focusVisible"]),m=n(11),y=n(539);const j={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"};var x=t=>{let{theme:e,ownerState:n}=t;const o=(t=>j[t]||t)(n.color),r=Object(m.b)(e,"palette.".concat(o),!1)||n.color,i=Object(m.b)(e,"palette.".concat(o,"Channel"));return"vars"in e&&i?"rgba(".concat(i," / 0.4)"):Object(y.a)(r,.4)},O=n(2);const w=["className","color","component","onBlur","onFocus","TypographyClasses","underline","variant","sx"],k=Object(l.a)(p.a,{name:"MuiLink",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:n}=t;return[e.root,e["underline".concat(Object(a.a)(n.underline))],"button"===n.component&&e.button]}})((t=>{let{theme:e,ownerState:n}=t;return Object(r.a)({},"none"===n.underline&&{textDecoration:"none"},"hover"===n.underline&&{textDecoration:"none","&:hover":{textDecoration:"underline"}},"always"===n.underline&&Object(r.a)({textDecoration:"underline"},"inherit"!==n.color&&{textDecorationColor:x({theme:e,ownerState:n})},{"&:hover":{textDecorationColor:"inherit"}}),"button"===n.component&&{position:"relative",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none","&::-moz-focus-inner":{borderStyle:"none"},["&.".concat(v.focusVisible)]:{outline:"auto"}})})),S=i.forwardRef((function(t,e){const n=Object(u.a)({props:t,name:"MuiLink"}),{className:l,color:p="primary",component:h="a",onBlur:g,onFocus:v,TypographyClasses:m,underline:y="always",variant:x="inherit",sx:S}=n,_=Object(o.a)(n,w),{isFocusVisibleRef:E,onBlur:I,onFocus:A,ref:M}=Object(f.a)(),[D,R]=i.useState(!1),F=Object(d.a)(e,M),T=Object(r.a)({},n,{color:p,component:h,focusVisible:D,underline:y,variant:x}),L=(t=>{const{classes:e,component:n,focusVisible:o,underline:r}=t,i={root:["root","underline".concat(Object(a.a)(r)),"button"===n&&"button",o&&"focusVisible"]};return Object(s.a)(i,b,e)})(T);return Object(O.jsx)(k,Object(r.a)({color:p,className:Object(c.a)(L.root,l),classes:m,component:h,onBlur:t=>{I(t),!1===E.current&&R(!1),g&&g(t)},onFocus:t=>{A(t),!0===E.current&&R(!0),v&&v(t)},ref:F,ownerState:T,variant:x,sx:[...Object.keys(j).includes(p)?[]:[{color:p}],...Array.isArray(S)?S:[S]]},_))}));e.a=S},569:function(t,e,n){"use strict";n.d(e,"a",(function(){return Nt}));var o=n(8),r=n(0);const i=/^[a-z0-9]+(-[a-z0-9]+)*$/,c=Object.freeze({left:0,top:0,width:16,height:16,rotate:0,vFlip:!1,hFlip:!1});function s(t){return Object(o.a)(Object(o.a)({},c),t)}const a=function(t,e,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";const r=t.split(":");if("@"===t.slice(0,1)){if(r.length<2||r.length>3)return null;o=r.shift().slice(1)}if(r.length>3||!r.length)return null;if(r.length>1){const t=r.pop(),n=r.pop(),i={provider:r.length>0?r[0]:o,prefix:n,name:t};return e&&!l(i)?null:i}const i=r[0],c=i.split("-");if(c.length>1){const t={provider:o,prefix:c.shift(),name:c.join("-")};return e&&!l(t)?null:t}if(n&&""===o){const t={provider:o,prefix:"",name:i};return e&&!l(t,n)?null:t}return null},l=(t,e)=>!!t&&!(""!==t.provider&&!t.provider.match(i)||!(e&&""===t.prefix||t.prefix.match(i))||!t.name.match(i));function u(t,e){const n=Object(o.a)({},t);for(const o in c){const t=o;if(void 0!==e[t]){const o=e[t];if(void 0===n[t]){n[t]=o;continue}switch(t){case"rotate":n[t]=(n[t]+o)%4;break;case"hFlip":case"vFlip":n[t]=o!==n[t];break;default:n[t]=o}}}return n}function f(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];function o(e,n){if(void 0!==t.icons[e])return Object.assign({},t.icons[e]);if(n>5)return null;const r=t.aliases;if(r&&void 0!==r[e]){const t=r[e],i=o(t.parent,n+1);return i?u(i,t):i}const i=t.chars;return!n&&i&&void 0!==i[e]?o(i[e],n+1):null}const r=o(e,0);if(r)for(const i in c)void 0===r[i]&&void 0!==t[i]&&(r[i]=t[i]);return r&&n?s(r):r}function d(t,e,n){n=n||{};const o=[];if("object"!==typeof t||"object"!==typeof t.icons)return o;t.not_found instanceof Array&&t.not_found.forEach((t=>{e(t,null),o.push(t)}));const r=t.icons;Object.keys(r).forEach((n=>{const r=f(t,n,!0);r&&(e(n,r),o.push(n))}));const i=n.aliases||"all";if("none"!==i&&"object"===typeof t.aliases){const n=t.aliases;Object.keys(n).forEach((r=>{if("variations"===i&&function(t){for(const e in c)if(void 0!==t[e])return!0;return!1}(n[r]))return;const s=f(t,r,!0);s&&(e(r,s),o.push(r))}))}return o}const p={provider:"string",aliases:"object",not_found:"object"};for(const zt in c)p[zt]=typeof c[zt];function h(t){if("object"!==typeof t||null===t)return null;const e=t;if("string"!==typeof e.prefix||!t.icons||"object"!==typeof t.icons)return null;for(const r in p)if(void 0!==t[r]&&typeof t[r]!==p[r])return null;const n=e.icons;for(const r in n){const t=n[r];if(!r.match(i)||"string"!==typeof t.body)return null;for(const e in c)if(void 0!==t[e]&&typeof t[e]!==typeof c[e])return null}const o=e.aliases;if(o)for(const r in o){const t=o[r],e=t.parent;if(!r.match(i)||"string"!==typeof e||!n[e]&&!o[e])return null;for(const n in c)if(void 0!==t[n]&&typeof t[n]!==typeof c[n])return null}return e}let g=Object.create(null);try{const t=window||self;t&&1===t._iconifyStorage.version&&(g=t._iconifyStorage.storage)}catch(Ct){}function b(t,e){void 0===g[t]&&(g[t]=Object.create(null));const n=g[t];return void 0===n[e]&&(n[e]=function(t,e){return{provider:t,prefix:e,icons:Object.create(null),missing:Object.create(null)}}(t,e)),n[e]}function v(t,e){if(!h(e))return[];const n=Date.now();return d(e,((e,o)=>{o?t.icons[e]=o:t.missing[e]=n}))}function m(t,e){const n=t.icons[e];return void 0===n?null:n}let y=!1;function j(t){return"boolean"===typeof t&&(y=t),y}function x(t){const e="string"===typeof t?a(t,!0,y):t;return e?m(b(e.provider,e.prefix),e.name):null}function O(t,e){const n=a(t,!0,y);if(!n)return!1;return function(t,e,n){try{if("string"===typeof n.body)return t.icons[e]=Object.freeze(s(n)),!0}catch(Ct){}return!1}(b(n.provider,n.prefix),n.name,e)}const w=Object.freeze({inline:!1,width:null,height:null,hAlign:"center",vAlign:"middle",slice:!1,hFlip:!1,vFlip:!1,rotate:0});function k(t,e){const n={};for(const o in t){const r=o;if(n[r]=t[r],void 0===e[r])continue;const i=e[r];switch(r){case"inline":case"slice":"boolean"===typeof i&&(n[r]=i);break;case"hFlip":case"vFlip":!0===i&&(n[r]=!n[r]);break;case"hAlign":case"vAlign":"string"===typeof i&&""!==i&&(n[r]=i);break;case"width":case"height":("string"===typeof i&&""!==i||"number"===typeof i&&i||null===i)&&(n[r]=i);break;case"rotate":"number"===typeof i&&(n[r]+=i)}}return n}const S=/(-?[0-9.]*[0-9]+[0-9.]*)/g,_=/^-?[0-9.]*[0-9]+[0-9.]*$/g;function E(t,e,n){if(1===e)return t;if(n=void 0===n?100:n,"number"===typeof t)return Math.ceil(t*e*n)/n;if("string"!==typeof t)return t;const o=t.split(S);if(null===o||!o.length)return t;const r=[];let i=o.shift(),c=_.test(i);for(;;){if(c){const t=parseFloat(i);isNaN(t)?r.push(i):r.push(Math.ceil(t*e*n)/n)}else r.push(i);if(i=o.shift(),void 0===i)return r.join("");c=!c}}function I(t){let e="";switch(t.hAlign){case"left":e+="xMin";break;case"right":e+="xMax";break;default:e+="xMid"}switch(t.vAlign){case"top":e+="YMin";break;case"bottom":e+="YMax";break;default:e+="YMid"}return e+=t.slice?" slice":" meet",e}function A(t,e){const n={left:t.left,top:t.top,width:t.width,height:t.height};let o,r,i=t.body;[t,e].forEach((t=>{const e=[],o=t.hFlip,r=t.vFlip;let c,s=t.rotate;switch(o?r?s+=2:(e.push("translate("+(n.width+n.left).toString()+" "+(0-n.top).toString()+")"),e.push("scale(-1 1)"),n.top=n.left=0):r&&(e.push("translate("+(0-n.left).toString()+" "+(n.height+n.top).toString()+")"),e.push("scale(1 -1)"),n.top=n.left=0),s<0&&(s-=4*Math.floor(s/4)),s%=4,s){case 1:c=n.height/2+n.top,e.unshift("rotate(90 "+c.toString()+" "+c.toString()+")");break;case 2:e.unshift("rotate(180 "+(n.width/2+n.left).toString()+" "+(n.height/2+n.top).toString()+")");break;case 3:c=n.width/2+n.left,e.unshift("rotate(-90 "+c.toString()+" "+c.toString()+")")}s%2===1&&(0===n.left&&0===n.top||(c=n.left,n.left=n.top,n.top=c),n.width!==n.height&&(c=n.width,n.width=n.height,n.height=c)),e.length&&(i='<g transform="'+e.join(" ")+'">'+i+"</g>")})),null===e.width&&null===e.height?(r="1em",o=E(r,n.width/n.height)):null!==e.width&&null!==e.height?(o=e.width,r=e.height):null!==e.height?(r=e.height,o=E(r,n.width/n.height)):(o=e.width,r=E(o,n.height/n.width)),"auto"===o&&(o=n.width),"auto"===r&&(r=n.height),o="string"===typeof o?o:o.toString()+"",r="string"===typeof r?r:r.toString()+"";const c={attributes:{width:o,height:r,preserveAspectRatio:I(e),viewBox:n.left.toString()+" "+n.top.toString()+" "+n.width.toString()+" "+n.height.toString()},body:i};return e.inline&&(c.inline=!0),c}const M=/\sid="(\S+)"/g,D="IconifyId"+Date.now().toString(16)+(16777216*Math.random()|0).toString(16);let R=0;function F(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:D;const n=[];let o;for(;o=M.exec(t);)n.push(o[1]);return n.length?(n.forEach((n=>{const o="function"===typeof e?e(n):e+(R++).toString(),r=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");t=t.replace(new RegExp('([#;"])('+r+')([")]|\\.[a-z])',"g"),"$1"+o+"$3")})),t):t}const T=Object.create(null);function L(t,e){T[t]=e}function N(t){return T[t]||T[""]}function C(t){let e;if("string"===typeof t.resources)e=[t.resources];else if(e=t.resources,!(e instanceof Array)||!e.length)return null;return{resources:e,path:void 0===t.path?"/":t.path,maxURL:t.maxURL?t.maxURL:500,rotate:t.rotate?t.rotate:750,timeout:t.timeout?t.timeout:5e3,random:!0===t.random,index:t.index?t.index:0,dataAfterTimeout:!1!==t.dataAfterTimeout}}const U=Object.create(null),z=["https://api.simplesvg.com","https://api.unisvg.com"],P=[];for(;z.length>0;)1===z.length||Math.random()>.5?P.push(z.shift()):P.push(z.pop());function B(t,e){const n=C(e);return null!==n&&(U[t]=n,!0)}function V(t){return U[t]}U[""]=C({resources:["https://api.iconify.design"].concat(P)});const $=(t,e)=>{let n=t,o=-1!==n.indexOf("?");return Object.keys(e).forEach((t=>{let r;try{r=function(t){switch(typeof t){case"boolean":return t?"true":"false";case"number":case"string":return encodeURIComponent(t);default:throw new Error("Invalid parameter")}}(e[t])}catch(Ct){return}n+=(o?"&":"?")+encodeURIComponent(t)+"="+r,o=!0})),n},q={},H={};let J=(()=>{let t;try{if(t=fetch,"function"===typeof t)return t}catch(Ct){}return null})();const W={prepare:(t,e,n)=>{const o=[];let r=q[e];void 0===r&&(r=function(t,e){const n=V(t);if(!n)return 0;let o;if(n.maxURL){let t=0;n.resources.forEach((e=>{const n=e;t=Math.max(t,n.length)}));const r=$(e+".json",{icons:""});o=n.maxURL-t-n.path.length-r.length}else o=0;const r=t+":"+e;return H[t]=n.path,q[r]=o,o}(t,e));const i="icons";let c={type:i,provider:t,prefix:e,icons:[]},s=0;return n.forEach(((n,a)=>{s+=n.length+1,s>=r&&a>0&&(o.push(c),c={type:i,provider:t,prefix:e,icons:[]},s=n.length),c.icons.push(n)})),o.push(c),o},send:(t,e,n)=>{if(!J)return void n("abort",424);let o=function(t){if("string"===typeof t){if(void 0===H[t]){const e=V(t);if(!e)return"/";H[t]=e.path}return H[t]}return"/"}(e.provider);switch(e.type){case"icons":{const t=e.prefix,n=e.icons.join(",");o+=$(t+".json",{icons:n});break}case"custom":{const t=e.uri;o+="/"===t.slice(0,1)?t.slice(1):t;break}default:return void n("abort",400)}let r=503;J(t+o).then((t=>{const e=t.status;if(200===e)return r=501,t.json();setTimeout((()=>{n(function(t){return 404===t}(e)?"abort":"next",e)}))})).then((t=>{"object"===typeof t&&null!==t?setTimeout((()=>{n("success",t)})):setTimeout((()=>{n("next",r)}))})).catch((()=>{n("next",r)}))}};const Y=Object.create(null),X=Object.create(null);function G(t,e){t.forEach((t=>{const n=t.provider;if(void 0===Y[n])return;const o=Y[n],r=t.prefix,i=o[r];i&&(o[r]=i.filter((t=>t.id!==e)))}))}let K=0;var Q={resources:[],index:0,timeout:2e3,rotate:750,random:!1,dataAfterTimeout:!1};function Z(t,e,n,o){const r=t.resources.length,i=t.random?Math.floor(Math.random()*r):t.index;let c;if(t.random){let e=t.resources.slice(0);for(c=[];e.length>1;){const t=Math.floor(Math.random()*e.length);c.push(e[t]),e=e.slice(0,t).concat(e.slice(t+1))}c=c.concat(e)}else c=t.resources.slice(i).concat(t.resources.slice(0,i));const s=Date.now();let a,l="pending",u=0,f=null,d=[],p=[];function h(){f&&(clearTimeout(f),f=null)}function g(){"pending"===l&&(l="aborted"),h(),d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function b(t,e){e&&(p=[]),"function"===typeof t&&p.push(t)}function v(){l="failed",p.forEach((t=>{t(void 0,a)}))}function m(){d.forEach((t=>{"pending"===t.status&&(t.status="aborted")})),d=[]}function y(){if("pending"!==l)return;h();const o=c.shift();if(void 0===o)return d.length?void(f=setTimeout((()=>{h(),"pending"===l&&(m(),v())}),t.timeout)):void v();const r={status:"pending",resource:o,callback:(e,n)=>{!function(e,n,o){const r="success"!==n;switch(d=d.filter((t=>t!==e)),l){case"pending":break;case"failed":if(r||!t.dataAfterTimeout)return;break;default:return}if("abort"===n)return a=o,void v();if(r)return a=o,void(d.length||(c.length?y():v()));if(h(),m(),!t.random){const n=t.resources.indexOf(e.resource);-1!==n&&n!==t.index&&(t.index=n)}l="completed",p.forEach((t=>{t(o)}))}(r,e,n)}};d.push(r),u++,f=setTimeout(y,t.rotate),n(o,e,r.callback)}return"function"===typeof o&&p.push(o),setTimeout(y),function(){return{startTime:s,payload:e,status:l,queriesSent:u,queriesPending:d.length,subscribe:b,abort:g}}}function tt(t){const e=function(t){if("object"!==typeof t||"object"!==typeof t.resources||!(t.resources instanceof Array)||!t.resources.length)throw new Error("Invalid Reduncancy configuration");const e=Object.create(null);let n;for(n in Q)void 0!==t[n]?e[n]=t[n]:e[n]=Q[n];return e}(t);let n=[];function o(){n=n.filter((t=>"pending"===t().status))}return{query:function(t,r,i){const c=Z(e,t,r,((t,e)=>{o(),i&&i(t,e)}));return n.push(c),c},find:function(t){const e=n.find((e=>t(e)));return void 0!==e?e:null},setIndex:t=>{e.index=t},getIndex:()=>e.index,cleanup:o}}function et(){}const nt=Object.create(null);function ot(t,e,n){let o,r;if("string"===typeof t){const e=N(t);if(!e)return n(void 0,424),et;r=e.send;const i=function(t){if(void 0===nt[t]){const e=V(t);if(!e)return;const n={config:e,redundancy:tt(e)};nt[t]=n}return nt[t]}(t);i&&(o=i.redundancy)}else{const e=C(t);if(e){o=tt(e);const n=N(t.resources?t.resources[0]:"");n&&(r=n.send)}}return o&&r?o.query(e,r,n)().abort:(n(void 0,424),et)}const rt={};function it(){}const ct=Object.create(null),st=Object.create(null),at=Object.create(null),lt=Object.create(null);function ut(t,e){void 0===at[t]&&(at[t]=Object.create(null));const n=at[t];n[e]||(n[e]=!0,setTimeout((()=>{n[e]=!1,function(t,e){void 0===X[t]&&(X[t]=Object.create(null));const n=X[t];n[e]||(n[e]=!0,setTimeout((()=>{if(n[e]=!1,void 0===Y[t]||void 0===Y[t][e])return;const o=Y[t][e].slice(0);if(!o.length)return;const r=b(t,e);let i=!1;o.forEach((n=>{const o=n.icons,c=o.pending.length;o.pending=o.pending.filter((n=>{if(n.prefix!==e)return!0;const c=n.name;if(void 0!==r.icons[c])o.loaded.push({provider:t,prefix:e,name:c});else{if(void 0===r.missing[c])return i=!0,!0;o.missing.push({provider:t,prefix:e,name:c})}return!1})),o.pending.length!==c&&(i||G([{provider:t,prefix:e}],n.id),n.callback(o.loaded.slice(0),o.missing.slice(0),o.pending.slice(0),n.abort))}))})))}(t,e)})))}const ft=Object.create(null);function dt(t,e,n){void 0===st[t]&&(st[t]=Object.create(null));const o=st[t];void 0===lt[t]&&(lt[t]=Object.create(null));const r=lt[t];void 0===ct[t]&&(ct[t]=Object.create(null));const i=ct[t];void 0===o[e]?o[e]=n:o[e]=o[e].concat(n).sort(),r[e]||(r[e]=!0,setTimeout((()=>{r[e]=!1;const n=o[e];delete o[e];const c=N(t);if(!c)return void function(){const n=(""===t?"":"@"+t+":")+e,o=Math.floor(Date.now()/6e4);ft[n]<o&&(ft[n]=o,console.error('Unable to retrieve icons for "'+n+'" because API is not configured properly.'))}();c.prepare(t,e,n).forEach((n=>{ot(t,n,((o,r)=>{const c=b(t,e);if("object"!==typeof o){if(404!==r)return;const t=Date.now();n.icons.forEach((e=>{c.missing[e]=t}))}else try{const n=v(c,o);if(!n.length)return;const r=i[e];n.forEach((t=>{delete r[t]})),rt.store&&rt.store(t,o)}catch(s){console.error(s)}ut(t,e)}))}))})))}const pt=(t,e)=>{const n=function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=[];return t.forEach((t=>{const r="string"===typeof t?a(t,!1,n):t;e&&!l(r,n)||o.push({provider:r.provider,prefix:r.prefix,name:r.name})})),o}(t,!0,j()),o=function(t){const e={loaded:[],missing:[],pending:[]},n=Object.create(null);t.sort(((t,e)=>t.provider!==e.provider?t.provider.localeCompare(e.provider):t.prefix!==e.prefix?t.prefix.localeCompare(e.prefix):t.name.localeCompare(e.name)));let o={provider:"",prefix:"",name:""};return t.forEach((t=>{if(o.name===t.name&&o.prefix===t.prefix&&o.provider===t.provider)return;o=t;const r=t.provider,i=t.prefix,c=t.name;void 0===n[r]&&(n[r]=Object.create(null));const s=n[r];void 0===s[i]&&(s[i]=b(r,i));const a=s[i];let l;l=void 0!==a.icons[c]?e.loaded:""===i||void 0!==a.missing[c]?e.missing:e.pending;const u={provider:r,prefix:i,name:c};l.push(u)})),e}(n);if(!o.pending.length){let t=!0;return e&&setTimeout((()=>{t&&e(o.loaded,o.missing,o.pending,it)})),()=>{t=!1}}const r=Object.create(null),i=[];let c,s;o.pending.forEach((t=>{const e=t.provider,n=t.prefix;if(n===s&&e===c)return;c=e,s=n,i.push({provider:e,prefix:n}),void 0===ct[e]&&(ct[e]=Object.create(null));const o=ct[e];void 0===o[n]&&(o[n]=Object.create(null)),void 0===r[e]&&(r[e]=Object.create(null));const a=r[e];void 0===a[n]&&(a[n]=[])}));const u=Date.now();return o.pending.forEach((t=>{const e=t.provider,n=t.prefix,o=t.name,i=ct[e][n];void 0===i[o]&&(i[o]=u,r[e][n].push(o))})),i.forEach((t=>{const e=t.provider,n=t.prefix;r[e][n].length&&dt(e,n,r[e][n])})),e?function(t,e,n){const o=K++,r=G.bind(null,n,o);if(!e.pending.length)return r;const i={id:o,icons:e,callback:t,abort:r};return n.forEach((t=>{const e=t.provider,n=t.prefix;void 0===Y[e]&&(Y[e]=Object.create(null));const o=Y[e];void 0===o[n]&&(o[n]=[]),o[n].push(i)})),r}(e,o,i):it},ht="iconify2",gt="iconify",bt=gt+"-count",vt=gt+"-version",mt=36e5,yt={local:!0,session:!0};let jt=!1;const xt={local:0,session:0},Ot={local:[],session:[]};let wt="undefined"===typeof window?{}:window;function kt(t){const e=t+"Storage";try{if(wt&&wt[e]&&"number"===typeof wt[e].length)return wt[e]}catch(Ct){}return yt[t]=!1,null}function St(t,e,n){try{return t.setItem(bt,n.toString()),xt[e]=n,!0}catch(Ct){return!1}}function _t(t){const e=t.getItem(bt);if(e){const t=parseInt(e);return t||0}return 0}const Et=()=>{if(jt)return;jt=!0;const t=Math.floor(Date.now()/mt)-168;function e(e){const n=kt(e);if(!n)return;const o=e=>{const o=gt+e.toString(),r=n.getItem(o);if("string"!==typeof r)return!1;let i=!0;try{const e=JSON.parse(r);if("object"!==typeof e||"number"!==typeof e.cached||e.cached<t||"string"!==typeof e.provider||"object"!==typeof e.data||"string"!==typeof e.data.prefix)i=!1;else{const t=e.provider,n=e.data.prefix;i=v(b(t,n),e.data).length>0}}catch(Ct){i=!1}return i||n.removeItem(o),i};try{const t=n.getItem(vt);if(t!==ht)return t&&function(t){try{const e=_t(t);for(let n=0;n<e;n++)t.removeItem(gt+n.toString())}catch(Ct){}}(n),void function(t,e){try{t.setItem(vt,ht)}catch(Ct){}St(t,e,0)}(n,e);let r=_t(n);for(let n=r-1;n>=0;n--)o(n)||(n===r-1?r--:Ot[e].push(n));St(n,e,r)}catch(Ct){}}for(const n in yt)e(n)},It=(t,e)=>{function n(n){if(!yt[n])return!1;const o=kt(n);if(!o)return!1;let r=Ot[n].shift();if(void 0===r&&(r=xt[n],!St(o,n,r+1)))return!1;try{const n={cached:Math.floor(Date.now()/mt),provider:t,data:e};o.setItem(gt+r.toString(),JSON.stringify(n))}catch(Ct){return!1}return!0}jt||Et(),Object.keys(e.icons).length&&(e.not_found&&delete(e=Object.assign({},e)).not_found,n("local")||n("session"))};const At=/[\s,]+/;function Mt(t,e){e.split(At).forEach((e=>{switch(e.trim()){case"horizontal":t.hFlip=!0;break;case"vertical":t.vFlip=!0}}))}function Dt(t,e){e.split(At).forEach((e=>{const n=e.trim();switch(n){case"left":case"center":case"right":t.hAlign=n;break;case"top":case"middle":case"bottom":t.vAlign=n;break;case"slice":case"crop":t.slice=!0;break;case"meet":t.slice=!1}}))}function Rt(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=t.replace(/^-?[0-9.]*/,"");function o(t){for(;t<0;)t+=4;return t%4}if(""===n){const e=parseInt(t);return isNaN(e)?0:o(e)}if(n!==t){let e=0;switch(n){case"%":e=25;break;case"deg":e=90}if(e){let r=parseFloat(t.slice(0,t.length-n.length));return isNaN(r)?0:(r/=e,r%1===0?o(r):0)}}return e}const Ft={xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink","aria-hidden":!0,role:"img",style:{}},Tt=Object(o.a)(Object(o.a)({},w),{},{inline:!0});if(j(!0),L("",W),"undefined"!==typeof document&&"undefined"!==typeof window){rt.store=It,Et();const t=window;if(void 0!==t.IconifyPreload){const e=t.IconifyPreload,n="Invalid IconifyPreload syntax.";"object"===typeof e&&null!==e&&(e instanceof Array?e:[e]).forEach((t=>{try{("object"!==typeof t||null===t||t instanceof Array||"object"!==typeof t.icons||"string"!==typeof t.prefix||!function(t,e){if("object"!==typeof t)return!1;if("string"!==typeof e&&(e="string"===typeof t.provider?t.provider:""),y&&""===e&&("string"!==typeof t.prefix||""===t.prefix)){let e=!1;return h(t)&&(t.prefix="",d(t,((t,n)=>{n&&O(t,n)&&(e=!0)}))),e}return!("string"!==typeof t.prefix||!l({provider:e,prefix:t.prefix,name:"a"}))&&!!v(b(e,t.prefix),t)}(t))&&console.error(n)}catch(e){console.error(n)}}))}if(void 0!==t.IconifyProviders){const e=t.IconifyProviders;if("object"===typeof e&&null!==e)for(let t in e){const n="IconifyProviders["+t+"] is invalid.";try{const o=e[t];if("object"!==typeof o||!o||void 0===o.resources)continue;B(t,o)||console.error(n)}catch(Ut){console.error(n)}}}}class Lt extends r.Component{constructor(t){super(t),this.state={icon:null}}_abortLoading(){this._loading&&(this._loading.abort(),this._loading=null)}_setData(t){this.state.icon!==t&&this.setState({icon:t})}_checkIcon(t){const e=this.state,n=this.props.icon;if("object"===typeof n&&null!==n&&"string"===typeof n.body)return this._icon="",this._abortLoading(),void((t||null===e.icon)&&this._setData({data:s(n)}));let o;if("string"!==typeof n||null===(o=a(n,!1,!0)))return this._abortLoading(),void this._setData(null);const r=x(o);if(null!==r){if(this._icon!==n||null===e.icon){this._abortLoading(),this._icon=n;const t=["iconify"];""!==o.prefix&&t.push("iconify--"+o.prefix),""!==o.provider&&t.push("iconify--"+o.provider),this._setData({data:r,classes:t}),this.props.onLoad&&this.props.onLoad(n)}}else this._loading&&this._loading.name===n||(this._abortLoading(),this._icon="",this._setData(null),this._loading={name:n,abort:pt([o],this._checkIcon.bind(this,!1))})}componentDidMount(){this._checkIcon(!1)}componentDidUpdate(t){t.icon!==this.props.icon&&this._checkIcon(!0)}componentWillUnmount(){this._abortLoading()}render(){const t=this.props,e=this.state.icon;if(null===e)return t.children?t.children:r.createElement("span",{});let n=t;return e.classes&&(n=Object(o.a)(Object(o.a)({},t),{},{className:("string"===typeof t.className?t.className+" ":"")+e.classes.join(" ")})),((t,e,n,i)=>{const c=n?Tt:w,s=k(c,e),a="object"===typeof e.style&&null!==e.style?e.style:{},l=Object(o.a)(Object(o.a)({},Ft),{},{ref:i,style:a});for(let o in e){const t=e[o];if(void 0!==t)switch(o){case"icon":case"style":case"children":case"onLoad":case"_ref":case"_inline":break;case"inline":case"hFlip":case"vFlip":s[o]=!0===t||"true"===t||1===t;break;case"flip":"string"===typeof t&&Mt(s,t);break;case"align":"string"===typeof t&&Dt(s,t);break;case"color":a.color=t;break;case"rotate":"string"===typeof t?s[o]=Rt(t):"number"===typeof t&&(s[o]=t);break;case"ariaHidden":case"aria-hidden":!0!==t&&"true"!==t&&delete l["aria-hidden"];break;default:void 0===c[o]&&(l[o]=t)}}const u=A(t,s);let f=0,d=e.id;"string"===typeof d&&(d=d.replace(/-/g,"_")),l.dangerouslySetInnerHTML={__html:F(u.body,d?()=>d+"ID"+f++:"iconifyReact")};for(let o in u.attributes)l[o]=u.attributes[o];return u.inline&&void 0===a.verticalAlign&&(a.verticalAlign="-0.125em"),r.createElement("svg",l)})(e.data,n,t._inline,t._ref)}}const Nt=r.forwardRef((function(t,e){const n=Object(o.a)(Object(o.a)({},t),{},{_ref:e,_inline:!1});return r.createElement(Lt,n)}));r.forwardRef((function(t,e){const n=Object(o.a)(Object(o.a)({},t),{},{_ref:e,_inline:!0});return r.createElement(Lt,n)}))},635:function(t,e,n){"use strict";var o=n(12),r=n(3),i=n(0),c=n(27),s=n(6),a=n(545),l=n(226),u=n(47),f=n(67),d=n(2);const p=["component","direction","spacing","divider","children"];function h(t,e){const n=i.Children.toArray(t).filter(Boolean);return n.reduce(((t,o,r)=>(t.push(o),r<n.length-1&&t.push(i.cloneElement(e,{key:"separator-".concat(r)})),t)),[])}const g=Object(u.a)("div",{name:"MuiStack",slot:"Root",overridesResolver:(t,e)=>[e.root]})((t=>{let{ownerState:e,theme:n}=t,o=Object(r.a)({display:"flex",flexDirection:"column"},Object(c.b)({theme:n},Object(c.e)({values:e.direction,breakpoints:n.breakpoints.values}),(t=>({flexDirection:t}))));if(e.spacing){const t=Object(s.a)(n),r=Object.keys(n.breakpoints.values).reduce(((t,n)=>(("object"===typeof e.spacing&&null!=e.spacing[n]||"object"===typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t)),{}),i=Object(c.e)({values:e.direction,base:r}),a=Object(c.e)({values:e.spacing,base:r});"object"===typeof i&&Object.keys(i).forEach(((t,e,n)=>{if(!i[t]){const o=e>0?i[n[e-1]]:"column";i[t]=o}}));const u=(n,o)=>{return{"& > :not(style) + :not(style)":{margin:0,["margin".concat((r=o?i[o]:e.direction,{row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"}[r]))]:Object(s.c)(t,n)}};var r};o=Object(l.a)(o,Object(c.b)({theme:n},a,u))}return o=Object(c.c)(n.breakpoints,o),o})),b=i.forwardRef((function(t,e){const n=Object(f.a)({props:t,name:"MuiStack"}),i=Object(a.a)(n),{component:c="div",direction:s="column",spacing:l=0,divider:u,children:b}=i,v=Object(o.a)(i,p),m={direction:s,spacing:l};return Object(d.jsx)(g,Object(r.a)({as:c,ownerState:m,ref:e},v,{children:u?h(b,u):b}))}));e.a=b}}]);
//# sourceMappingURL=5.35ecbbf9.chunk.js.map