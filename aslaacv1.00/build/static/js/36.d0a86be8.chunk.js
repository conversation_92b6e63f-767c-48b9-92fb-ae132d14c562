(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[36],{1286:function(t,e,a){"use strict";a.r(e),a.d(e,"default",(function(){return d}));var r=a(47),n=a(611),i=a(521),o=a(567),c=a(0),s=a(2);const l=Object(c.lazy)((()=>a.e(40).then(a.bind(null,738)))),p=Object(c.lazy)((()=>Promise.all([a.e(0),a.e(1),a.e(2),a.e(3),a.e(17)]).then(a.bind(null,1249)))),u=Object(r.a)("div")((t=>{let{theme:e}=t;return{maxWidth:480,margin:"auto",display:"flex",minHeight:"100vh",flexDirection:"column",justifyContent:"center",alignContent:"space-between",gap:3}}));function d(){return Object(s.jsx)(o.a,{title:"Forgot Password",children:Object(s.jsxs)(u,{children:[Object(s.jsx)(n.a,{variant:"h3",gutterBottom:!0,textAlign:"center",children:"\u0428\u0438\u043d\u044d \u043d\u0443\u0443\u0446 \u04af\u0433 \u04af\u04af\u0441\u0433\u044d\u0445"}),Object(s.jsx)(i.a,{width:"50%",sx:{mx:"auto",mb:3,mt:-3},children:Object(s.jsx)(l,{})}),Object(s.jsx)(n.a,{paragraph:!0,textAlign:"center",children:"\u0422\u0430\u043d\u044b \u043d\u0443\u0443\u0446 \u04af\u0433\u044d\u044d \u0441\u044d\u0440\u0433\u044d\u044d\u0445\u0438\u0439\u043d \u0442\u0443\u043b\u0434 \u0442\u04e9\u0445\u04e9\u04e9\u0440\u04e9\u043c\u0436\u0438\u0439\u043d \u0434\u0443\u0433\u0430\u0430\u0440\u044b\u043d \u0441\u04af\u04af\u043b\u0438\u0439\u043d 6 \u043e\u0440\u043e\u043d\u0433 \u043e\u0440\u0443\u0443\u043b\u043d\u0430 \u0443\u0443. \u0425\u044d\u0440\u044d\u0432 \u0442\u0430 \u0442\u04e9\u0445\u04e9\u04e9\u0440\u04e9\u043c\u0436\u0438\u0439\u043d \u0434\u0443\u0433\u0430\u0430\u0440\u044b\u0433 \u043c\u044d\u0434\u044d\u0445\u0433\u04af\u0439 \u0431\u043e\u043b \u0442\u0430 \u043c\u0430\u0448\u0438\u043d\u0440\u0443\u0443\u0433\u0430\u0430 \u043c\u0435\u0441\u0441\u0435\u0436\u044d\u044d\u0440 id \u0433\u044d\u0436 \u0431\u0438\u0447\u044d\u044d\u0434 \u0438\u043b\u0433\u044d\u044d\u0436 \u0430\u0432\u043d\u0430 \u0443\u0443. \u0416\u0438\u0447 \u0445\u0430\u0440\u0438\u0443 \u0430\u0432\u0430\u0445\u0430\u0434 \u043c\u0430\u0448\u0438\u043d\u0434 \u043d\u044d\u0433\u0436 \u0445\u043e\u043d\u043e\u0433 \u0431\u0430\u0439\u0445\u044b\u0433 \u0430\u043d\u0445\u0430\u0430\u0440\u043d\u0430 \u0443\u0443.        "}),Object(s.jsxs)(i.a,{width:"80%",mx:"auto",my:3,children:[Object(s.jsx)(p,{})," "]})]})})}},551:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var r=a(12);function n(t,e){if(null==t)return{};var a,n,i=Object(r.a)(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)a=o[n],-1===e.indexOf(a)&&{}.propertyIsEnumerable.call(t,a)&&(i[a]=t[a])}return i}},567:function(t,e,a){"use strict";var r=a(8),n=a(551),i=a(7),o=a.n(i),c=a(232),s=a(0),l=a(521),p=a(610),u=a(2);const d=["children","title","meta"],h=Object(s.forwardRef)(((t,e)=>{let{children:a,title:i="",meta:o}=t,s=Object(n.a)(t,d);return Object(u.jsxs)(u.Fragment,{children:[Object(u.jsxs)(c.a,{children:[Object(u.jsx)("title",{children:i}),o]}),Object(u.jsx)(l.a,Object(r.a)(Object(r.a)({ref:e},s),{},{children:Object(u.jsx)(p.a,{children:a})}))]})}));h.propTypes={children:o.a.node.isRequired,title:o.a.string,meta:o.a.node},e.a=h},568:function(t,e,a){"use strict";var r=a(180);const n=Object(r.a)();e.a=n},610:function(t,e,a){"use strict";var r=a(12),n=a(3),i=a(0),o=a(31),c=a(225),s=a(516),l=a(541),p=a(512),u=a(568),d=a(519),h=a(2);const b=["className","component","disableGutters","fixed","maxWidth","classes"],m=Object(d.a)(),g=Object(u.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,e["maxWidth".concat(Object(c.a)(String(a.maxWidth)))],a.fixed&&e.fixed,a.disableGutters&&e.disableGutters]}}),j=t=>Object(p.a)({props:t,name:"MuiContainer",defaultTheme:m}),x=(t,e)=>{const{classes:a,fixed:r,disableGutters:n,maxWidth:i}=t,o={root:["root",i&&"maxWidth".concat(Object(c.a)(String(i))),r&&"fixed",n&&"disableGutters"]};return Object(l.a)(o,(t=>Object(s.a)(e,t)),a)};var O=a(52),f=a(47),v=a(67);const y=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{createStyledComponent:e=g,useThemeProps:a=j,componentName:c="MuiContainer"}=t,s=e((t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",display:"block"},!a.disableGutters&&{paddingLeft:e.spacing(2),paddingRight:e.spacing(2),[e.breakpoints.up("sm")]:{paddingLeft:e.spacing(3),paddingRight:e.spacing(3)}})}),(t=>{let{theme:e,ownerState:a}=t;return a.fixed&&Object.keys(e.breakpoints.values).reduce(((t,a)=>{const r=a,n=e.breakpoints.values[r];return 0!==n&&(t[e.breakpoints.up(r)]={maxWidth:"".concat(n).concat(e.breakpoints.unit)}),t}),{})}),(t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({},"xs"===a.maxWidth&&{[e.breakpoints.up("xs")]:{maxWidth:Math.max(e.breakpoints.values.xs,444)}},a.maxWidth&&"xs"!==a.maxWidth&&{[e.breakpoints.up(a.maxWidth)]:{maxWidth:"".concat(e.breakpoints.values[a.maxWidth]).concat(e.breakpoints.unit)}})})),l=i.forwardRef((function(t,e){const i=a(t),{className:l,component:p="div",disableGutters:u=!1,fixed:d=!1,maxWidth:m="lg"}=i,g=Object(r.a)(i,b),j=Object(n.a)({},i,{component:p,disableGutters:u,fixed:d,maxWidth:m}),O=x(j,c);return Object(h.jsx)(s,Object(n.a)({as:p,ownerState:j,className:Object(o.a)(O.root,l),ref:e},g))}));return l}({createStyledComponent:Object(f.a)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,e["maxWidth".concat(Object(O.a)(String(a.maxWidth)))],a.fixed&&e.fixed,a.disableGutters&&e.disableGutters]}}),useThemeProps:t=>Object(v.a)({props:t,name:"MuiContainer"})});e.a=y},611:function(t,e,a){"use strict";var r=a(12),n=a(3),i=a(0),o=a(31),c=a(545),s=a(541),l=a(47),p=a(67),u=a(52),d=a(542),h=a(516);function b(t){return Object(h.a)("MuiTypography",t)}Object(d.a)("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);var m=a(2);const g=["align","className","component","gutterBottom","noWrap","paragraph","variant","variantMapping"],j=Object(l.a)("span",{name:"MuiTypography",slot:"Root",overridesResolver:(t,e)=>{const{ownerState:a}=t;return[e.root,a.variant&&e[a.variant],"inherit"!==a.align&&e["align".concat(Object(u.a)(a.align))],a.noWrap&&e.noWrap,a.gutterBottom&&e.gutterBottom,a.paragraph&&e.paragraph]}})((t=>{let{theme:e,ownerState:a}=t;return Object(n.a)({margin:0},a.variant&&e.typography[a.variant],"inherit"!==a.align&&{textAlign:a.align},a.noWrap&&{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},a.gutterBottom&&{marginBottom:"0.35em"},a.paragraph&&{marginBottom:16})})),x={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},O={primary:"primary.main",textPrimary:"text.primary",secondary:"secondary.main",textSecondary:"text.secondary",error:"error.main"},f=i.forwardRef((function(t,e){const a=Object(p.a)({props:t,name:"MuiTypography"}),i=(t=>O[t]||t)(a.color),l=Object(c.a)(Object(n.a)({},a,{color:i})),{align:d="inherit",className:h,component:f,gutterBottom:v=!1,noWrap:y=!1,paragraph:W=!1,variant:w="body1",variantMapping:S=x}=l,k=Object(r.a)(l,g),R=Object(n.a)({},l,{align:d,color:i,className:h,component:f,gutterBottom:v,noWrap:y,paragraph:W,variant:w,variantMapping:S}),M=f||(W?"p":S[w]||x[w])||"span",B=(t=>{const{align:e,gutterBottom:a,noWrap:r,paragraph:n,variant:i,classes:o}=t,c={root:["root",i,"inherit"!==t.align&&"align".concat(Object(u.a)(e)),a&&"gutterBottom",r&&"noWrap",n&&"paragraph"]};return Object(s.a)(c,b,o)})(R);return Object(m.jsx)(j,Object(n.a)({as:M,ref:e,ownerState:R,className:Object(o.a)(B.root,h)},k))}));e.a=f}}]);
//# sourceMappingURL=36.d0a86be8.chunk.js.map