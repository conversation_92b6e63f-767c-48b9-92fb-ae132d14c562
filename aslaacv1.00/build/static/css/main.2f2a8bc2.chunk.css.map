{"version": 3, "sources": ["webpack://src/swiper.min.css", "webpack://src/index.css", "webpack://src/styles/carControl.css"], "names": [], "mappings": "AAYC,WAAW,wBAAwB,CAAC,4rEAA4rE,CAAC,eAAe,CAAC,iBAAiB,CAAC,MAAM,4BAA4B,CAAC,QAAQ,gBAAgB,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,SAAS,CAAC,SAAS,CAAC,iCAAiC,qBAAqB,CAAC,gBAAgB,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,6BAA6B,CAAC,kBAAsB,CAAC,8CAA8C,uBAA8B,CAAC,uBAAuB,kBAAkB,CAAC,uCAAuC,kBAAkB,CAAC,cAAc,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,8BAA8B,iBAAiB,CAAC,oDAAoD,WAAW,CAAC,mCAAmC,sBAAsB,CAAC,oCAAoC,CAAC,sCAAsC,uBAAuB,CAAC,kCAAkC,CAAC,0BAA0B,CAAC,sDAAsD,kBAAkB,CAAC,yQAAyQ,2BAA2B,CAAC,sLAAsL,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,mBAAmB,CAAC,UAAU,CAAC,gCAAgC,0BAA0B,CAAC,qCAAqC,mEAAsE,CAAC,sCAAsC,kEAAuE,CAAC,oCAAoC,iEAAqE,CAAC,uCAAuC,mEAAwE,CAAC,iCAAiC,aAAa,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,oDAAoD,YAAY,CAAC,+CAA+C,6BAA6B,CAAC,mDAAmD,4BAA4B,CAAC,iDAAiD,4BAA4B,CAAC,wCAAyC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,6EAA6E,yDAAuD,CAAvD,wDAAwD,CAAC,0DAA2D,WAAW,CAAC,cAAc,CAAC,yCAAyC,CAAC,2EAA2E,0DAAsD,CAAtD,uDAAuD,CAAC,wDAAyD,UAAU,CAAC,aAAa,CAAC,0CAA0C,CAAC,+CAA+C,+BAA+B,CCXp0K,gBACI,QAAS,CACT,SAAU,CACV,8DAA0E,CAC1E,gBACJ,CAEA,2DACI,YACJ,CAEA,+BACI,qCACJ,CAEA,iBACI,WAAY,CACZ,kCAAoC,CACpC,oBAAuB,CACvB,iBACJ,CClBA,cAEE,eAAgB,CAChB,mBAAqB,CACrB,sCACF,CAEA,kCANE,0CAYF,CANA,oBAEE,eAAgB,CAChB,gBAAiB,CACjB,oBAAsB,CACtB,sCACF,CAEA,oBACE,0CAA6C,CAC7C,eAAgB,CAChB,gBAAkB,CAClB,oBAAsB,CACtB,sCACF,CAGA,iBAEE,eAAgB,CAChB,oBACF,CAEA,uCALE,6CASF,CAJA,sBAEE,eAAgB,CAChB,oBACF,CAGA,gBACE,8DAAkE,CAClE,eAAgB,CAChB,oBAAsB,CACtB,eACF,CAGA,oBAEE,eAAgB,CAChB,gBAAiB,CACjB,oBAAsB,CACtB,wBAAyB,CACzB,sCACF,CAGA,mCATE,0CAgBF,CAPA,eAEE,eAAgB,CAChB,gBAAiB,CACjB,mBAAqB,CACrB,uCAA4C,CAC5C,UACF,CAGA,iBACE,0CAA6C,CAC7C,eAAgB,CAChB,mBACF,CAEA,sBACE,UAAc,CACd,oCACF,CAEA,wBACE,UAAc,CACd,sCACF,CAEA,qBACE,SAAc,CACd,oCACF,CAGA,qBACE,0CAA6C,CAC7C,eAAgB,CAChB,oBAAsB,CACtB,aAAc,CACd,sCACF,CAGA,gBACE,6CAAgD,CAChD,eAAgB,CAChB,oBAAsB,CACtB,eACF,CAGA,aACE,gDAAmD,CACnD,eAAgB,CAChB,oBAAsB,CACtB,gBAAkB,CAClB,aACF,CAGA,qBACE,6CAAgD,CAChD,eAAgB,CAChB,mBAAqB,CACrB,wBACF,CAGA,kBACE,0CAA6C,CAC7C,eAAgB,CAChB,eAAiB,CACjB,oBAAsB,CACtB,wBACF,CAEA,yBACE,UAAc,CACd,oCACF,CAEA,0BACE,SAAc,CACd,oCACF,CAEA,6BACE,UAAc,CACd,sCACF,CAGA,aACE,6CAAgD,CAChD,eAAgB,CAChB,oBAAsB,CACtB,eACF,CAGA,kBACE,gDAAmD,CACnD,eAAgB,CAChB,oBAAsB,CACtB,eAAiB,CACjB,wBAAyB,CACzB,aAAc,CACd,sCACF,CAGA,yBACE,oBACE,cAAe,CACf,oBACF,CAEA,eACE,gBAAiB,CACjB,oBACF,CAEA,oBACE,gBAAkB,CAClB,mBACF,CAEA,cACE,eAAiB,CACjB,oBACF,CAEA,iBACE,gBACF,CAEA,gBACE,eACF,CACF,CAGA,6BACE,sCAA2C,CAC3C,eACF,CAEA,qCACE,oCACF,CAEA,oCACE,oCACF,CAGA,wBACE,MACE,sCACF,CACA,IACE,uCACF,CACF,CAEA,uBACE,8CACF,CAGA,gBACE,yBAA8B,CAC9B,eAAgB,CAChB,iBAAkB,CAClB,mCACF", "file": "main.2f2a8bc2.chunk.css", "sourcesContent": ["/**\n * Swiper 8.3.1\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2022 Vladimir Kharlampidi\n *\n * Released under the MIT License\n *\n * Released on: July 13, 2022\n */\n\n @font-face{font-family:swiper-icons;src:url('data:application/font-woff;charset=utf-8;base64, 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');font-weight:400;font-style:normal}:root{--swiper-theme-color:#007aff}.swiper{margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;box-sizing:content-box}.swiper-android .swiper-slide,.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-pointer-events{touch-action:pan-y}.swiper-pointer-events.swiper-vertical{touch-action:pan-x}.swiper-slide{flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform}.swiper-slide-invisible-blank{visibility:hidden}.swiper-autoheight,.swiper-autoheight .swiper-slide{height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden .swiper-slide{transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d,.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d .swiper-slide,.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top,.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top{position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}.swiper-3d .swiper-slide-shadow{background:rgba(0,0,0,.15)}.swiper-3d .swiper-slide-shadow-left{background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-right{background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-top{background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-3d .swiper-slide-shadow-bottom{background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode>.swiper-wrapper>.swiper-slide{scroll-snap-align:start start}.swiper-horizontal.swiper-css-mode>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-vertical.swiper-css-mode>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}.swiper-centered>.swiper-wrapper>.swiper-slide{scroll-snap-align:center center}", "/* Global body and root styling to remove default black background */\nbody, html, #root {\n    margin: 0;\n    padding: 0;\n    background: linear-gradient(135deg, #0f2027 0%, #203a43 50%, #2c5364 100%);\n    min-height: 100vh;\n}\n\n.leaflet-control-container .leaflet-routing-container-hide {\n    display: none;\n}\n\n.leaflet-tooltip-bottom:before {\n    border-bottom-color: #171c20 !important;\n}\n\n.leaflet-tooltip {\n    border: none;\n    background-color: #171c20 !important;\n    color: white !important;\n    text-align: center;\n}\n\n", "/* Car Control System Fonts and Styling */\n\n/* Digital Display Font Classes */\n.digital-font {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 600;\n  letter-spacing: 0.1em;\n  text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);\n}\n\n.digital-font-large {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 700;\n  font-size: 1.2rem;\n  letter-spacing: 0.15em;\n  text-shadow: 0 0 8px rgba(0, 255, 255, 0.4);\n}\n\n.digital-font-small {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 500;\n  font-size: 0.85rem;\n  letter-spacing: 0.08em;\n  text-shadow: 0 0 3px rgba(0, 255, 255, 0.2);\n}\n\n/* Automotive Data Font Classes */\n.automotive-font {\n  font-family: 'Roboto Mono', monospace !important;\n  font-weight: 500;\n  letter-spacing: 0.05em;\n}\n\n.automotive-font-bold {\n  font-family: 'Roboto Mono', monospace !important;\n  font-weight: 700;\n  letter-spacing: 0.08em;\n}\n\n/* Technical Information Font Classes */\n.technical-font {\n  font-family: 'JetBrains Mono', 'Roboto Mono', monospace !important;\n  font-weight: 400;\n  letter-spacing: 0.02em;\n  font-size: 0.9rem;\n}\n\n/* Car Status Display */\n.car-status-display {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 700;\n  font-size: 1.1rem;\n  letter-spacing: 0.12em;\n  text-transform: uppercase;\n  text-shadow: 0 0 6px rgba(0, 255, 255, 0.3);\n}\n\n/* Speed and Measurement Display */\n.speed-display {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 800;\n  font-size: 1.3rem;\n  letter-spacing: 0.2em;\n  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);\n  color: #00ffff;\n}\n\n/* Voltage and Battery Display */\n.voltage-display {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 600;\n  letter-spacing: 0.1em;\n}\n\n.voltage-display.high {\n  color: #00ff00;\n  text-shadow: 0 0 5px rgba(0, 255, 0, 0.4);\n}\n\n.voltage-display.medium {\n  color: #ffff00;\n  text-shadow: 0 0 5px rgba(255, 255, 0, 0.4);\n}\n\n.voltage-display.low {\n  color: #ff0000;\n  text-shadow: 0 0 5px rgba(255, 0, 0, 0.4);\n}\n\n/* Temperature Display */\n.temperature-display {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 600;\n  letter-spacing: 0.08em;\n  color: #ff8c00;\n  text-shadow: 0 0 4px rgba(255, 140, 0, 0.3);\n}\n\n/* Signal Strength Display */\n.signal-display {\n  font-family: 'Roboto Mono', monospace !important;\n  font-weight: 600;\n  letter-spacing: 0.05em;\n  font-size: 0.9rem;\n}\n\n/* GPS Coordinates Display */\n.gps-display {\n  font-family: 'JetBrains Mono', monospace !important;\n  font-weight: 500;\n  letter-spacing: 0.03em;\n  font-size: 0.85rem;\n  color: #00bfff;\n}\n\n/* Control Button Text */\n.control-button-text {\n  font-family: 'Roboto Mono', monospace !important;\n  font-weight: 700;\n  letter-spacing: 0.1em;\n  text-transform: uppercase;\n}\n\n/* Status Indicators */\n.status-indicator {\n  font-family: 'Orbitron', monospace !important;\n  font-weight: 600;\n  font-size: 0.8rem;\n  letter-spacing: 0.15em;\n  text-transform: uppercase;\n}\n\n.status-indicator.online {\n  color: #00ff00;\n  text-shadow: 0 0 5px rgba(0, 255, 0, 0.4);\n}\n\n.status-indicator.offline {\n  color: #ff0000;\n  text-shadow: 0 0 5px rgba(255, 0, 0, 0.4);\n}\n\n.status-indicator.connecting {\n  color: #ffff00;\n  text-shadow: 0 0 5px rgba(255, 255, 0, 0.4);\n}\n\n/* Device Information Display */\n.device-info {\n  font-family: 'Roboto Mono', monospace !important;\n  font-weight: 500;\n  letter-spacing: 0.04em;\n  font-size: 0.9rem;\n}\n\n/* Protocol Display */\n.protocol-display {\n  font-family: 'JetBrains Mono', monospace !important;\n  font-weight: 600;\n  letter-spacing: 0.08em;\n  font-size: 0.8rem;\n  text-transform: uppercase;\n  color: #00bfff;\n  text-shadow: 0 0 3px rgba(0, 191, 255, 0.3);\n}\n\n/* Mobile Responsive Adjustments */\n@media (max-width: 600px) {\n  .digital-font-large {\n    font-size: 1rem;\n    letter-spacing: 0.12em;\n  }\n  \n  .speed-display {\n    font-size: 1.1rem;\n    letter-spacing: 0.15em;\n  }\n  \n  .car-status-display {\n    font-size: 0.95rem;\n    letter-spacing: 0.1em;\n  }\n  \n  .digital-font {\n    font-size: 0.9rem;\n    letter-spacing: 0.08em;\n  }\n  \n  .automotive-font {\n    font-size: 0.85rem;\n  }\n  \n  .technical-font {\n    font-size: 0.8rem;\n  }\n}\n\n/* High contrast mode for better visibility */\n.high-contrast .digital-font {\n  text-shadow: 0 0 8px rgba(0, 255, 255, 0.6);\n  font-weight: 700;\n}\n\n.high-contrast .voltage-display.high {\n  text-shadow: 0 0 8px rgba(0, 255, 0, 0.6);\n}\n\n.high-contrast .voltage-display.low {\n  text-shadow: 0 0 8px rgba(255, 0, 0, 0.6);\n}\n\n/* Animation for digital displays */\n@keyframes digital-glow {\n  0%, 100% {\n    text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);\n  }\n  50% {\n    text-shadow: 0 0 10px rgba(0, 255, 255, 0.6);\n  }\n}\n\n.digital-font.animated {\n  animation: digital-glow 2s ease-in-out infinite;\n}\n\n/* Car dashboard style background for text */\n.dashboard-text {\n  background: rgba(0, 0, 0, 0.7);\n  padding: 2px 6px;\n  border-radius: 3px;\n  border: 1px solid rgba(0, 255, 255, 0.3);\n}\n"]}